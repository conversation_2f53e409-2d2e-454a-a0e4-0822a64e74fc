---
id: cloc-sdk
title: Cloc SDK
sidebar_label: Cloc SDK
sidebar_position: 2
---

import Preview from "@site/src/components/cloc-sdk/preview";
import Tabs from "@theme/Tabs";
import TabItem from "@theme/TabItem";
import ComponentPreview from "@site/src/components/ui/component-preview";

# Ever Cloc SDK

Transform your productivity applications with the Ever Cloc SDK, a comprehensive development toolkit designed for building sophisticated time tracking and productivity solutions. Whether you're creating focused work timers, project management systems, or enterprise-grade productivity suites, our SDK provides the professional-grade building blocks you need.

## Introduction

The Ever Cloc SDK is a powerful, modula r library that empowers developers to integrate enterprise-level time tracking capabilities into their applications. Built for modern JavaScript environments including Node.js and React, it offers a complete ecosystem of ready-to-use components, utilities, and services that seamlessly integrate into your existing development workflow.

:::tip Developer-First Design
Our SDK is built by developers, for developers. Every component is designed with developer experience in mind, featuring comprehensive TypeScript support, intuitive APIs, and extensive customization options.
:::

## SDK Packages Overview

The Ever Cloc SDK consists of five core packages, each designed for specific functionality while working together as a cohesive ecosystem:

### [**@cloc/atoms**](/docs/core-libraries/cloc-atoms)

_React Components and UI Elements_

The atoms package provides a comprehensive library of React components for building time tracking interfaces, analytics dashboards, and reports.

**Use Cases:**

- Building custom time trackers
- Creating user authentication flows
- Implementing team management interfaces
- Building analytics and reporting interfaces

### [**@cloc/ui**](/docs/core-libraries/cloc-ui)

_Core UI Component Library_

A foundational UI library providing essential components and design system elements. This includes : Buttons, inputs, modals, and navigation elements, ...

**Use Cases:**

- Building consistent user interfaces
- Implementing design systems

### [**@cloc/types**](/docs/core-libraries/cloc-types)

_TypeScript Type Definitions_

Comprehensive TypeScript definitions ensuring type safety across the entire SDK ecosystem. This includes: API Types, Component Props, Data Models, Configuration Types, and Event Types.

### [**@cloc/api**](/docs/core-libraries/cloc-api)

_API Client and Integration Utilities_

A powerful API client providing seamless integration with Ever Cloc backend services.

**Use Cases:**

- Integrating with Ever Cloc backend services
- Building custom API integrations and more

### [**@cloc/tracking**](/docs/core-libraries/cloc-tracking)

_Analytics and User Interaction Tracking_

Advanced analytics library built on top of clarity-js for comprehensive user behavior tracking.

**Key Features:**

- **Interaction Tracking**: Click, scroll, and navigation pattern analysis
- **Session Recording**: Complete session replay capabilities
- **Heatmap Generation**: Visual representation of user interaction patterns
- **Performance Monitoring**: Page load times and user experience metrics
- **Behavioral Analytics**: User journey analysis and engagement scoring

**Use Cases:**

- Understanding user behavior and application usage
- Optimizing user experience and interface design
- Monitoring application performance
- Generating insights for product development

## Getting Started

### Installation

Install the packages you need for your project:

```bash
# Install all packages
npm install @cloc/atoms @cloc/ui @cloc/types @cloc/api @cloc/tracking

# Or install specific packages
npm install @cloc/atoms @cloc/ui
```

### Quick Start Example

<ComponentPreview code={
`
import { ClocProvider, ModernCloc } from "@cloc/atoms";
import { tracker } from "@cloc/tracking";

// Initialize tracking
tracker.start({
    organizationId: "your-org-id",
    tenantId: "your-tenant-id",
    token: "your-auth-token",
});

function App() {
    return (
        <ClocProvider>
            <h1>My Time Tracking App</h1>
            <ModernCloc expanded showProgress /> 
        </ClocProvider>
    ); 
}
`
} language="jsx" >
    <Preview />
</ComponentPreview>

## Next Steps

:::info Explore the SDK

- **[Component Library](/docs/components)** - Browse all available React components
- **[API Reference](/docs/api-reference)** - Complete API documentation and examples
- **[Tracking Guide](/docs/tracking-package)** - Implement advanced analytics and tracking
- **[Examples](/docs/examples)** - Real-world implementation examples and tutorials
- **[Migration Guide](/docs/migration)** - Upgrade from previous versions
  :::

### Community & Resources

- **[Storybook](https://storybook.cloc.ai)** - Interactive component documentation
- **[GitHub Repository](https://github.com/ever-co/ever-cloc)** - Source code and issue tracking
- **[Community Chat](https://gitter.im/ever-co/ever-cloc)** - Connect with other developers
- **[Blog](https://cloc.ai/blog)** - Latest updates and development insights
