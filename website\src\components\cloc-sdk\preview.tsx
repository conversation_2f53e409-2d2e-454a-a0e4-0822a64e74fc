import { Cloc<PERSON>rovider, ModernCloc } from "@cloc/atoms";

// export default function App() {
//   return (
//     <div
//       style={{
//         background: "linear-gradient(90deg, #4f8cff 0%, #34e89e 100%)",
//         padding: "32px",
//         borderRadius: "12px",
//         boxShadow: "0 4px 24px rgba(0,0,0,0.12)",
//       }}
//     >
//       <ClocProvider>
//         <div className="app">
//           <h1>My Time Tracking App</h1>
//           <ModernCloc expanded showProgress />
//         </div>
//       </ClocProvider>
//     </div>
//   );
// }

export default function App() {
  return (
    <div
      style={{
        background: "linear-gradient(90deg, #4f8cff 0%, #34e89e 100%)",
        padding: "32px",
        borderRadius: "12px",
        boxShadow: "0 4px 24px rgba(0,0,0,0.12)",
      }}
    >
      <h3>Preview</h3>
    </div>
  );
}
